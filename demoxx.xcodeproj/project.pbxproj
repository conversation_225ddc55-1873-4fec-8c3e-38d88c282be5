// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXContainerItemProxy section */
		257CD6FD2E6DE89C00B0F874 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 257CD6E72E6DE89B00B0F874 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 257CD6EE2E6DE89B00B0F874;
			remoteInfo = demoxx;
		};
		257CD7072E6DE89C00B0F874 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 257CD6E72E6DE89B00B0F874 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 257CD6EE2E6DE89B00B0F874;
			remoteInfo = demoxx;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		257CD6EF2E6DE89B00B0F874 /* demoxx.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = demoxx.app; sourceTree = BUILT_PRODUCTS_DIR; };
		257CD6FC2E6DE89C00B0F874 /* demoxxTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = demoxxTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		257CD7062E6DE89C00B0F874 /* demoxxUITests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = demoxxUITests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		257CD6F12E6DE89B00B0F874 /* demoxx */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = demoxx;
			sourceTree = "<group>";
		};
		257CD6FF2E6DE89C00B0F874 /* demoxxTests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = demoxxTests;
			sourceTree = "<group>";
		};
		257CD7092E6DE89C00B0F874 /* demoxxUITests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = demoxxUITests;
			sourceTree = "<group>";
		};
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		257CD6EC2E6DE89B00B0F874 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		257CD6F92E6DE89C00B0F874 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		257CD7032E6DE89C00B0F874 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		257CD6E62E6DE89B00B0F874 = {
			isa = PBXGroup;
			children = (
				257CD6F12E6DE89B00B0F874 /* demoxx */,
				257CD6FF2E6DE89C00B0F874 /* demoxxTests */,
				257CD7092E6DE89C00B0F874 /* demoxxUITests */,
				257CD6F02E6DE89B00B0F874 /* Products */,
			);
			sourceTree = "<group>";
		};
		257CD6F02E6DE89B00B0F874 /* Products */ = {
			isa = PBXGroup;
			children = (
				257CD6EF2E6DE89B00B0F874 /* demoxx.app */,
				257CD6FC2E6DE89C00B0F874 /* demoxxTests.xctest */,
				257CD7062E6DE89C00B0F874 /* demoxxUITests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		257CD6EE2E6DE89B00B0F874 /* demoxx */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 257CD7102E6DE89C00B0F874 /* Build configuration list for PBXNativeTarget "demoxx" */;
			buildPhases = (
				257CD6EB2E6DE89B00B0F874 /* Sources */,
				257CD6EC2E6DE89B00B0F874 /* Frameworks */,
				257CD6ED2E6DE89B00B0F874 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				257CD6F12E6DE89B00B0F874 /* demoxx */,
			);
			name = demoxx;
			packageProductDependencies = (
			);
			productName = demoxx;
			productReference = 257CD6EF2E6DE89B00B0F874 /* demoxx.app */;
			productType = "com.apple.product-type.application";
		};
		257CD6FB2E6DE89C00B0F874 /* demoxxTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 257CD7132E6DE89C00B0F874 /* Build configuration list for PBXNativeTarget "demoxxTests" */;
			buildPhases = (
				257CD6F82E6DE89C00B0F874 /* Sources */,
				257CD6F92E6DE89C00B0F874 /* Frameworks */,
				257CD6FA2E6DE89C00B0F874 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				257CD6FE2E6DE89C00B0F874 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				257CD6FF2E6DE89C00B0F874 /* demoxxTests */,
			);
			name = demoxxTests;
			packageProductDependencies = (
			);
			productName = demoxxTests;
			productReference = 257CD6FC2E6DE89C00B0F874 /* demoxxTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		257CD7052E6DE89C00B0F874 /* demoxxUITests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 257CD7162E6DE89C00B0F874 /* Build configuration list for PBXNativeTarget "demoxxUITests" */;
			buildPhases = (
				257CD7022E6DE89C00B0F874 /* Sources */,
				257CD7032E6DE89C00B0F874 /* Frameworks */,
				257CD7042E6DE89C00B0F874 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				257CD7082E6DE89C00B0F874 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				257CD7092E6DE89C00B0F874 /* demoxxUITests */,
			);
			name = demoxxUITests;
			packageProductDependencies = (
			);
			productName = demoxxUITests;
			productReference = 257CD7062E6DE89C00B0F874 /* demoxxUITests.xctest */;
			productType = "com.apple.product-type.bundle.ui-testing";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		257CD6E72E6DE89B00B0F874 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1640;
				LastUpgradeCheck = 1640;
				TargetAttributes = {
					257CD6EE2E6DE89B00B0F874 = {
						CreatedOnToolsVersion = 16.4;
					};
					257CD6FB2E6DE89C00B0F874 = {
						CreatedOnToolsVersion = 16.4;
						TestTargetID = 257CD6EE2E6DE89B00B0F874;
					};
					257CD7052E6DE89C00B0F874 = {
						CreatedOnToolsVersion = 16.4;
						TestTargetID = 257CD6EE2E6DE89B00B0F874;
					};
				};
			};
			buildConfigurationList = 257CD6EA2E6DE89B00B0F874 /* Build configuration list for PBXProject "demoxx" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 257CD6E62E6DE89B00B0F874;
			minimizedProjectReferenceProxies = 1;
			preferredProjectObjectVersion = 77;
			productRefGroup = 257CD6F02E6DE89B00B0F874 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				257CD6EE2E6DE89B00B0F874 /* demoxx */,
				257CD6FB2E6DE89C00B0F874 /* demoxxTests */,
				257CD7052E6DE89C00B0F874 /* demoxxUITests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		257CD6ED2E6DE89B00B0F874 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		257CD6FA2E6DE89C00B0F874 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		257CD7042E6DE89C00B0F874 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		257CD6EB2E6DE89B00B0F874 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		257CD6F82E6DE89C00B0F874 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		257CD7022E6DE89C00B0F874 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		257CD6FE2E6DE89C00B0F874 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 257CD6EE2E6DE89B00B0F874 /* demoxx */;
			targetProxy = 257CD6FD2E6DE89C00B0F874 /* PBXContainerItemProxy */;
		};
		257CD7082E6DE89C00B0F874 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 257CD6EE2E6DE89B00B0F874 /* demoxx */;
			targetProxy = 257CD7072E6DE89C00B0F874 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		257CD70E2E6DE89C00B0F874 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				DEVELOPMENT_TEAM = SWS8AMQ6P9;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		257CD70F2E6DE89C00B0F874 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEVELOPMENT_TEAM = SWS8AMQ6P9;
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		257CD7112E6DE89C00B0F874 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = SWS8AMQ6P9;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.ltt.demoxx;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		257CD7122E6DE89C00B0F874 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = SWS8AMQ6P9;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.ltt.demoxx;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		257CD7142E6DE89C00B0F874 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = SWS8AMQ6P9;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.ltt.demoxxTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/demoxx.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/demoxx";
			};
			name = Debug;
		};
		257CD7152E6DE89C00B0F874 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = SWS8AMQ6P9;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.ltt.demoxxTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/demoxx.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/demoxx";
			};
			name = Release;
		};
		257CD7172E6DE89C00B0F874 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = SWS8AMQ6P9;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.ltt.demoxxUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = demoxx;
			};
			name = Debug;
		};
		257CD7182E6DE89C00B0F874 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = SWS8AMQ6P9;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.ltt.demoxxUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = demoxx;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		257CD6EA2E6DE89B00B0F874 /* Build configuration list for PBXProject "demoxx" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				257CD70E2E6DE89C00B0F874 /* Debug */,
				257CD70F2E6DE89C00B0F874 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		257CD7102E6DE89C00B0F874 /* Build configuration list for PBXNativeTarget "demoxx" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				257CD7112E6DE89C00B0F874 /* Debug */,
				257CD7122E6DE89C00B0F874 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		257CD7132E6DE89C00B0F874 /* Build configuration list for PBXNativeTarget "demoxxTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				257CD7142E6DE89C00B0F874 /* Debug */,
				257CD7152E6DE89C00B0F874 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		257CD7162E6DE89C00B0F874 /* Build configuration list for PBXNativeTarget "demoxxUITests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				257CD7172E6DE89C00B0F874 /* Debug */,
				257CD7182E6DE89C00B0F874 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 257CD6E72E6DE89B00B0F874 /* Project object */;
}
